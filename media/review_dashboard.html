<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>代码审查看板 - 待评价记录</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            color: white;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.95);
            padding: 20px;
            border-radius: 12px;
            text-align: center;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #666;
            font-size: 0.9rem;
        }

        .controls {
            background: rgba(255, 255, 255, 0.95);
            padding: 20px;
            border-radius: 12px;
            margin-bottom: 30px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
        }

        .controls h3 {
            margin-bottom: 15px;
            color: #333;
        }

        .filter-group {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            align-items: center;
        }

        .filter-group label {
            font-weight: 500;
            color: #555;
        }

        .filter-group input, .filter-group select {
            padding: 8px 12px;
            border: 2px solid #e1e5e9;
            border-radius: 6px;
            font-size: 14px;
            transition: border-color 0.3s;
        }

        .filter-group input:focus, .filter-group select:focus {
            outline: none;
            border-color: #667eea;
        }

        .btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.3s;
        }

        .btn:hover {
            background: #5a6fd8;
        }

        .btn-secondary {
            background: #6c757d;
        }

        .btn-secondary:hover {
            background: #5a6268;
        }

        .records-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
        }

        .records-header {
            background: #f8f9fa;
            padding: 15px 20px;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .records-header h3 {
            color: #333;
            margin: 0;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }

        .no-records {
            text-align: center;
            padding: 60px 20px;
            color: #666;
        }

        .no-records h3 {
            margin-bottom: 10px;
            color: #333;
        }

        .records-table {
            width: 100%;
            border-collapse: collapse;
        }

        .records-table th {
            background: #f8f9fa;
            padding: 12px 15px;
            text-align: left;
            font-weight: 600;
            color: #333;
            border-bottom: 2px solid #e9ecef;
        }

        .records-table td {
            padding: 12px 15px;
            border-bottom: 1px solid #e9ecef;
            vertical-align: top;
        }

        .records-table tr:hover {
            background: #f8f9fa;
        }

        .commit-url {
            max-width: 300px;
            word-break: break-all;
            color: #667eea;
            text-decoration: none;
        }

        .commit-url:hover {
            text-decoration: underline;
        }

        .user-name {
            font-weight: 500;
            color: #333;
        }

        .project-name {
            color: #666;
            font-size: 0.9rem;
        }

        .branch-name {
            background: #e9ecef;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.8rem;
            color: #666;
        }



        .reviewers {
            max-width: 200px;
            word-break: break-all;
        }

        .reviewer-tag {
            display: inline-block;
            background: #667eea;
            color: white;
            padding: 2px 6px;
            border-radius: 8px;
            font-size: 0.7rem;
            margin: 1px;
        }

        .date {
            color: #666;
            font-size: 0.9rem;
        }

        .actions {
            display: flex;
            gap: 5px;
        }

        .action-btn {
            padding: 4px 8px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.8rem;
            transition: background 0.3s;
        }

        .btn-useful {
            background: #51cf66;
            color: white;
        }

        .btn-useful:hover {
            background: #40c057;
        }

        .btn-not-useful {
            background: #ff6b6b;
            color: white;
        }

        .btn-not-useful:hover {
            background: #fa5252;
        }

        .btn-test {
            background: #ffd43b;
            color: #333;
        }

        .btn-test:hover {
            background: #fcc419;
        }

        .pagination {
            display: flex;
            justify-content: center;
            gap: 10px;
            padding: 20px;
            background: #f8f9fa;
            border-top: 1px solid #e9ecef;
        }

        .page-btn {
            padding: 8px 12px;
            border: 1px solid #dee2e6;
            background: white;
            cursor: pointer;
            border-radius: 4px;
            transition: all 0.3s;
        }

        .page-btn:hover {
            background: #e9ecef;
        }

        .page-btn.active {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }

        .page-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }

            .header h1 {
                font-size: 2rem;
            }

            .stats {
                grid-template-columns: 1fr;
            }

            .filter-group {
                flex-direction: column;
                align-items: stretch;
            }

            .records-table {
                font-size: 0.9rem;
            }

            .records-table th,
            .records-table td {
                padding: 8px 10px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>代码审查看板</h1>
            <p id="headerDescription">待评价的代码审查记录</p>
        </div>

        <div class="stats">
            <div class="stat-card">
                <div class="stat-number" id="totalRecords">-</div>
                <div class="stat-label">总记录数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="pendingRecords">-</div>
                <div class="stat-label">待评价记录</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="usefulRecords">-</div>
                <div class="stat-label">有用记录</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="notUsefulRecords">-</div>
                <div class="stat-label">无用记录</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="testRecords">-</div>
                <div class="stat-label">测试代码</div>
            </div>
        </div>

        <div class="controls">
            <h3>筛选条件</h3>
            <div class="filter-group">
                <label>
                    提交人:
                    <input type="text" id="userFilter" placeholder="输入用户名">
                </label>
                <label>
                    项目名:
                    <input type="text" id="projectFilter" placeholder="输入项目名">
                </label>
                <label>
                    审核人:
                    <input type="text" id="reviewerFilter" placeholder="输入审核人姓名">
                </label>
                <button class="btn" onclick="loadRecords()">筛选</button>
                <button class="btn btn-secondary" onclick="clearFilters()">清除筛选</button>
            </div>
        </div>

        <div class="records-container">
            <div class="records-header">
                <h3>待评价记录列表</h3>
                <div>
                    <span id="currentPageInfo">第 1 页，共 1 页</span>
                </div>
            </div>

            <div id="recordsContent">
                <div class="loading">加载中...</div>
            </div>

            <div class="pagination" id="pagination" style="display: none;">
                <button class="page-btn" onclick="changePage(-1)">上一页</button>
                <span id="pageNumbers"></span>
                <button class="page-btn" onclick="changePage(1)">下一页</button>
            </div>
        </div>
    </div>

    <script>
        let currentPage = 1;
        let totalPages = 1;
        let currentRecords = [];
        let filteredRecords = [];

        // 页面加载时获取数据
        document.addEventListener('DOMContentLoaded', function() {
            loadGroups();
            loadStats();
            loadRecords();
        });

        // 获取当前组别
        function getCurrentGroup() {
            const urlParams = new URLSearchParams(window.location.search);
            return urlParams.get('group');
        }
        
        // 加载组配置
        async function loadGroups() {
            try {
                const response = await fetch('/api/groups');
                const data = await response.json();
                
                if (data.success) {
                    // 检查URL参数中的组名
                    const groupFromUrl = getCurrentGroup();
                    if (groupFromUrl && data.groups[groupFromUrl]) {
                        updateHeaderDescription(groupFromUrl, data.groups[groupFromUrl].name);
                        displayGroupInfo(groupFromUrl, data.groups[groupFromUrl].name);
                    }
                }
            } catch (error) {
                console.error('加载组配置失败:', error);
            }
        }
        
        // 更新页面标题描述
        function updateHeaderDescription(groupKey, groupName) {
            const headerDescription = document.getElementById('headerDescription');
            if (groupKey) {
                headerDescription.textContent = `${groupName} - 待评价的代码审查记录`;
            } else {
                headerDescription.textContent = '代码审查看板 - 请指定组别';
            }
        }
        
        // 显示当前组信息
        function displayGroupInfo(groupKey, groupName) {
            const currentGroup = getCurrentGroup();
            if (currentGroup) {
                // 可以在这里添加显示组信息的逻辑
                console.log(`当前查看组: ${groupName} (${groupKey})`);
            }
        }

        // 加载统计数据
        async function loadStats() {
            try {
                const currentGroup = getCurrentGroup();
                let url = '/api/stats/review';
                if (currentGroup) {
                    url += `?group=${encodeURIComponent(currentGroup)}`;
                }
                
                const response = await fetch(url);
                const stats = await response.json();
                
                document.getElementById('totalRecords').textContent = stats.total || 0;
                document.getElementById('pendingRecords').textContent = stats.pending || 0;
                document.getElementById('usefulRecords').textContent = stats.useful || 0;
                document.getElementById('notUsefulRecords').textContent = stats.not_useful || 0;
                document.getElementById('testRecords').textContent = stats.test || 0;
            } catch (error) {
                console.error('加载统计数据失败:', error);
            }
        }

        // 加载记录
        async function loadRecords() {
            const currentGroup = getCurrentGroup();
            const userFilter = document.getElementById('userFilter').value;
            const projectFilter = document.getElementById('projectFilter').value;
            const reviewerFilter = document.getElementById('reviewerFilter').value;

            document.getElementById('recordsContent').innerHTML = '<div class="loading">加载中...</div>';

            try {
                let url = `/api/review/pending?page=${currentPage}`;
                const params = new URLSearchParams();
                
                if (currentGroup) params.append('group', currentGroup);
                if (userFilter) params.append('user_name', userFilter);
                if (projectFilter) params.append('project_name', projectFilter);
                if (reviewerFilter) params.append('reviewer_name', reviewerFilter);
                
                if (params.toString()) {
                    url += '&' + params.toString();
                }

                const response = await fetch(url);
                const data = await response.json();
                
                if (data.success) {
                    currentRecords = data.records || [];
                    totalPages = data.total_pages || 1;
                    displayRecords();
                    updatePagination();
                } else {
                    document.getElementById('recordsContent').innerHTML = 
                        '<div class="no-records"><h3>加载失败</h3><p>' + (data.message || '未知错误') + '</p></div>';
                }
            } catch (error) {
                console.error('加载记录失败:', error);
                document.getElementById('recordsContent').innerHTML = 
                    '<div class="no-records"><h3>加载失败</h3><p>网络错误，请稍后重试</p></div>';
            }
        }

        // 显示记录
        function displayRecords() {
            const container = document.getElementById('recordsContent');
            const currentGroup = getCurrentGroup();
            
            // 如果没有指定组别，显示提示信息
            if (!currentGroup) {
                container.innerHTML = '<div class="no-records"><h3>请指定组别</h3><p>请通过URL参数指定要查看的组别，例如：<br><code>?group=self_operated</code></p><p>支持的组别：</p><ul><li>self_operated - 自营组</li><li>channel - 渠道组</li><li>harmony - 鸿蒙组</li></ul></div>';
                return;
            }
            
            if (currentRecords.length === 0) {
                container.innerHTML = '<div class="no-records"><h3>暂无记录</h3><p>没有找到符合条件的待评价记录</p></div>';
                return;
            }

            let html = '<table class="records-table">';
            html += '<thead><tr>';
            html += '<th>提交信息</th>';
            html += '<th>提交人</th>';
            html += '<th>项目</th>';
            html += '<th>审核人员</th>';
            html += '<th>创建时间</th>';
            html += '<th>操作</th>';
            html += '</tr></thead><tbody>';

            currentRecords.forEach(record => {
                html += '<tr>';
                html += '<td>';
                // 从commit_url中提取gitlab_url
                const gitlabUrl = extractGitlabUrl(record.commit_url);
                html += '<div><a href="' + gitlabUrl + '" target="_blank" class="commit-url">' + 
                        (record.commit_title || '查看提交') + '</a></div>';
                html += '<div class="branch-name">' + (record.branch_name || '未知分支') + '</div>';
                html += '</td>';
                html += '<td><span class="user-name">' + (record.user_name || '未知用户') + '</span></td>';
                html += '<td><div class="project-name">' + (record.project_name || '未知项目') + '</div></td>';
                html += '<td class="reviewers">';
                if (record.reviewers && record.reviewers.length > 0) {
                    record.reviewers.forEach(reviewer => {
                        html += '<span class="reviewer-tag">' + reviewer + '</span>';
                    });
                } else {
                    html += '<span style="color: #999;">无</span>';
                }
                html += '</td>';
                html += '<td><span class="date">' + formatDate(record.created_at) + '</span></td>';
                html += '<td><div class="actions">';
                html += '<button class="action-btn btn-useful" onclick="markUseful(\'' + record.commit_url + '\', 1)">有用</button>';
                html += '<button class="action-btn btn-not-useful" onclick="markUseful(\'' + record.commit_url + '\', 0)">无用</button>';
                html += '<button class="action-btn btn-test" onclick="markUseful(\'' + record.commit_url + '\', 2)">测试</button>';
                html += '</div></td>';
                html += '</tr>';
            });

            html += '</tbody></table>';
            container.innerHTML = html;
        }

        // 更新分页
        function updatePagination() {
            const pagination = document.getElementById('pagination');
            const pageInfo = document.getElementById('currentPageInfo');
            const pageNumbers = document.getElementById('pageNumbers');

            if (totalPages <= 1) {
                pagination.style.display = 'none';
                pageInfo.textContent = '共 ' + currentRecords.length + ' 条记录';
                return;
            }

            pagination.style.display = 'flex';
            pageInfo.textContent = `第 ${currentPage} 页，共 ${totalPages} 页`;

            let pageHtml = '';
            for (let i = 1; i <= totalPages; i++) {
                if (i === currentPage) {
                    pageHtml += '<button class="page-btn active">' + i + '</button>';
                } else {
                    pageHtml += '<button class="page-btn" onclick="goToPage(' + i + ')">' + i + '</button>';
                }
            }
            pageNumbers.innerHTML = pageHtml;
        }

        // 切换页面
        function changePage(delta) {
            const newPage = currentPage + delta;
            if (newPage >= 1 && newPage <= totalPages) {
                currentPage = newPage;
                loadRecords();
            }
        }

        // 跳转到指定页面
        function goToPage(page) {
            if (page >= 1 && page <= totalPages) {
                currentPage = page;
                loadRecords();
            }
        }

        // 清除筛选条件
        function clearFilters() {
            document.getElementById('userFilter').value = '';
            document.getElementById('projectFilter').value = '';
            document.getElementById('reviewerFilter').value = '';
            currentPage = 1;
            loadRecords();
        }

        // 标记有用性
        async function markUseful(commitUrl, usefulValue) {
            try {
                const response = await fetch('/api/review/useful', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        commit_url: commitUrl,
                        useful: usefulValue
                    })
                });

                const data = await response.json();
                
                if (data.success) {
                    loadStats();
                    loadRecords();
                } else {
                    alert('标记失败：' + (data.message || '未知错误'));
                }
            } catch (error) {
                console.error('标记失败:', error);
                alert('网络错误，请稍后重试');
            }
        }

        // 从commit_url中提取gitlab_url
        function extractGitlabUrl(commitUrl) {
            if (!commitUrl) return '#';
            
            try {
                const url = new URL(commitUrl);
                const gitlabUrl = url.searchParams.get('gitlab_url');
                return gitlabUrl || commitUrl; // 如果没有gitlab_url参数，返回原始URL
            } catch (error) {
                console.error('解析URL失败:', error);
                return commitUrl; // 解析失败时返回原始URL
            }
        }

        // 格式化日期
        function formatDate(dateString) {
            if (!dateString) return '未知';
            
            const date = new Date(dateString);
            const now = new Date();
            const diff = now - date;
            
            if (diff < 60000) { // 1分钟内
                return '刚刚';
            } else if (diff < 3600000) { // 1小时内
                return Math.floor(diff / 60000) + '分钟前';
            } else if (diff < 86400000) { // 1天内
                return Math.floor(diff / 3600000) + '小时前';
            } else if (diff < 2592000000) { // 30天内
                return Math.floor(diff / 86400000) + '天前';
            } else {
                return date.toLocaleDateString('zh-CN');
            }
        }
    </script>
</body>
</html> 