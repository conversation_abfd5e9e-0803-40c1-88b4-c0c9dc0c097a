import os
import json

from flask import request
from datetime import datetime

from codereview_app import db, api_app
from sqlalchemy import inspect as sqla_inspect
from sqlalchemy.exc import OperationalError


class ApiUsage(db.Model):
    __tablename__ = 'api_usage'
    id = db.Column(db.Integer, primary_key=True)
    endpoint = db.Column(db.String(100), nullable=False, unique=True)
    call_count = db.Column(db.Integer, default=0)
    last_called = db.Column(db.DateTime)
    issue_count = db.Column(db.Integer, default=0)

    def to_dict(self):
        """将对象转为字典便于JSON序列化"""
        return {
            'id': self.id,
            'endpoint': self.endpoint,
            'api_name': self.api_name,
            'call_count': self.call_count,
            'last_called': self.last_called.isoformat() if self.last_called else None,
            'issue_count': self.issue_count
        }


class ReviewRecord(db.Model):
    __tablename__ = 'review_record'
    id = db.Column(db.Integer, primary_key=True)
    commit_url = db.Column(db.String(500), nullable=False, unique=True)
    useful = db.Column(db.Integer, default=None)  # 1=有用, 0=没用, 2=测试代码
    issue_count = db.Column(db.Integer, default=0)  # 问题个数
    user_name = db.Column(db.String(100))  # 用户名
    commit_title = db.Column(db.String(500))  # 提交标题
    gitlab_project_url = db.Column(db.String(500))  # GitLab项目URL
    project_name = db.Column(db.String(200))  # 项目名称
    branch_name = db.Column(db.String(100))  # 分支名
    reviewers = db.Column(db.Text)  # 审核人员列表，JSON格式存储
    created_at = db.Column(db.DateTime, default=datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)

    def to_dict(self):
        """将对象转为字典便于JSON序列化"""
        useful_map = {1: '有用', 0: '没用', 2: '测试代码'}
        return {
            'id': self.id,
            'commit_url': self.commit_url,
            'useful': self.useful,
            'useful_text': useful_map.get(self.useful, '未评价'),
            'issue_count': self.issue_count,
            'user_name': self.user_name,
            'commit_title': self.commit_title,
            'gitlab_project_url': self.gitlab_project_url,
            'project_name': self.project_name,
            'branch_name': self.branch_name,
            'reviewers': self.get_reviewers_list(),
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
    
    def get_reviewers_list(self):
        """获取审核人员列表"""
        if not self.reviewers:
            return []
        try:
            return json.loads(self.reviewers)
        except (json.JSONDecodeError, TypeError):
            return []
    
    def set_reviewers_list(self, reviewers_list):
        """设置审核人员列表"""
        if reviewers_list:
            self.reviewers = json.dumps(reviewers_list, ensure_ascii=False)
        else:
            self.reviewers = None


def init_db():
    """安全初始化数据库，避免重复创建表"""
    engine = db.engine

    try:
        inspector = sqla_inspect(engine)
        existing_tables = inspector.get_table_names()

        # 检查api_usage表
        if 'api_usage' not in existing_tables:
            print("创建api_usage表...")
            db.create_all()
            print("api_usage表创建完成")
        else:
            print("api_usage表已存在")
            _check_table_columns('api_usage', [
                'id', 'endpoint', 'call_count', 'last_called', 'issue_count'
            ], inspector)

        # 检查review_record表
        if 'review_record' not in existing_tables:
            print("创建review_record表...")
            db.create_all()
            print("review_record表创建完成")
        else:
            print("review_record表已存在")
            _check_table_columns('review_record', [
                'id', 'commit_url', 'useful', 'issue_count', 'user_name', 
                'commit_title', 'gitlab_project_url', 'project_name', 'branch_name', 'reviewers', 'created_at', 'updated_at'
            ], inspector)

    except OperationalError as e:
        print(f"数据库连接错误: {str(e)}")
        if 'no such table' in str(e).lower():
            print("尝试创建所有表...")
            db.create_all()
            print("表创建完成")
        else:
            print("无法连接数据库，请检查配置")


def _check_table_columns(table_name, expected_columns, inspector):
    """检查表的列是否完整"""
    columns = [col['name'] for col in inspector.get_columns(table_name)]
    missing_columns = [col for col in expected_columns if col not in columns]
    
    if missing_columns:
        print(f"警告：{table_name}表中缺少列 {', '.join(missing_columns)}")
        print("请使用数据库迁移工具添加这些列")


# 应用启动时初始化数据库
with api_app.app_context():
    init_db()


# 基础统计（统计所有端点）
@api_app.before_request
def track_api_usage():
    if request.endpoint and request.endpoint != 'static' and not request.endpoint.startswith('stats'):
        update_counter(request.endpoint)


def update_counter(endpoint):
    """更新接口的调用计数"""
    record = ApiUsage.query.filter_by(endpoint=endpoint).first()
    if not record:
        record = ApiUsage(endpoint=endpoint)
        db.session.add(record)
        db.session.flush()

    record.call_count += 1
    record.last_called = datetime.now()
    db.session.commit()


def update_issue_count(endpoint, count_delta=1):
    """更新问题个数（可指定增量，默认为+1）"""
    with api_app.app_context():
        record = ApiUsage.query.filter_by(endpoint=endpoint).first()
        if record:
            record.issue_count += count_delta
            db.session.commit()


def add_review_record(commit_url, issue_count=0, user_name=None, commit_title=None, gitlab_project_url=None, project_name=None, branch_name=None, reviewers=None, useful=None):
    """新增review记录
    
    Args:
        commit_url: GitLab提交URL
        issue_count: 问题个数
        user_name: 用户名
        commit_title: 提交标题
        gitlab_project_url: GitLab项目URL
        project_name: 项目名称
        branch_name: 分支名
        reviewers: 审核人员列表
        useful: 有用性值 (1=有用, 0=没用, 2=测试代码, None=未评价)
    """
    with api_app.app_context():
        try:
            # 检查是否已存在
            existing_record = ReviewRecord.query.filter_by(commit_url=commit_url).first()
            if existing_record:
                print(f"Review记录已存在: {commit_url}")
                return existing_record.id
            
            # 创建新记录
            record = ReviewRecord(
                commit_url=commit_url,
                issue_count=issue_count,
                user_name=user_name,
                commit_title=commit_title,
                gitlab_project_url=gitlab_project_url,
                project_name=project_name,
                branch_name=branch_name,
                useful=useful
            )
            
            # 设置审核人员列表
            if reviewers:
                record.set_reviewers_list(reviewers)
            
            db.session.add(record)
            db.session.commit()
            
            useful_text = "测试代码" if useful == 2 else "未评价" if useful is None else "有用" if useful == 1 else "没用"
            print(f"新增Review记录: {commit_url}, ID: {record.id}, 审核人员: {reviewers}, useful: {useful_text}")
            return record.id
            
        except Exception as e:
            db.session.rollback()
            print(f"新增Review记录失败: {str(e)}")
            return None


def update_review_useful(commit_url, useful_value):
    """更新review记录的useful字段
    
    Args:
        commit_url: GitLab提交URL
        useful_value: 有用性值 (1=有用, 0=没用, 2=测试代码)
        
    Returns:
        tuple: (success: bool, record_dict: dict or None)
    """
    with api_app.app_context():
        try:
            # 验证useful_value的有效性
            if useful_value not in [0, 1, 2]:
                print(f"无效的useful值: {useful_value}")
                return False, None
            
            record = ReviewRecord.query.filter_by(commit_url=commit_url).first()
            if not record:
                print(f"未找到Review记录: {commit_url}")
                return False, None
            
            record.useful = useful_value
            record.updated_at = datetime.now()
            db.session.commit()
            
            # 转换为字典以避免Session绑定问题
            record_dict = record.to_dict()
            
            useful_map = {1: '有用', 0: '没用', 2: '测试代码'}
            print(f"更新Review记录成功: {commit_url}, useful: {useful_map[useful_value]}")
            return True, record_dict
            
        except Exception as e:
            db.session.rollback()
            print(f"更新Review记录失败: {str(e)}")
            return False, None


def get_review_record(commit_url):
    """根据commit_url获取review记录"""
    with api_app.app_context():
        try:
            record = ReviewRecord.query.filter_by(commit_url=commit_url).first()
            return record.to_dict() if record else None
        except Exception as e:
            print(f"查询Review记录失败: {str(e)}")
            return None
