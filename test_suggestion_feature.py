#!/usr/bin/env python3
"""
测试建议功能的脚本
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from lib.model.ApiUsage import add_review_record, update_review_useful, get_review_record
import json

def test_suggestion_feature():
    """测试建议功能"""
    print("=== 测试建议功能 ===")
    
    # 测试数据
    test_commit_url = "https://test.gitlab.com/project/repo/-/commit/test123"
    test_suggestion = "建议优化代码结构，提高可读性"
    
    try:
        # 1. 创建一个测试记录
        print("\n1. 创建测试记录...")
        record_id = add_review_record(
            commit_url=test_commit_url,
            issue_count=3,
            user_name="测试用户",
            commit_title="测试提交",
            gitlab_project_url="https://test.gitlab.com/project/repo",
            project_name="测试项目",
            branch_name="feature/test",
            reviewers=["reviewer1", "reviewer2"]
        )
        print(f"创建记录成功，ID: {record_id}")
        
        # 2. 测试标记为"无用"并添加建议
        print("\n2. 测试标记为无用并添加建议...")
        success, record_dict = update_review_useful(test_commit_url, 0, test_suggestion)
        if success:
            print("标记为无用成功")
            print(f"建议内容: {record_dict['suggestion']}")
        else:
            print("标记失败")
            return False
            
        # 3. 验证记录
        print("\n3. 验证记录...")
        record = get_review_record(test_commit_url)
        if record:
            print(f"记录验证成功:")
            print(f"  - useful: {record['useful']} ({record['useful_text']})")
            print(f"  - suggestion: {record['suggestion']}")
        else:
            print("记录验证失败")
            return False
            
        # 4. 测试标记为"有用"（应该清空建议）
        print("\n4. 测试标记为有用（应该清空建议）...")
        success, record_dict = update_review_useful(test_commit_url, 1)
        if success:
            print("标记为有用成功")
            print(f"建议内容: {record_dict['suggestion']}")
            if record_dict['suggestion'] is None:
                print("✓ 建议字段已正确清空")
            else:
                print("✗ 建议字段未清空")
                return False
        else:
            print("标记失败")
            return False
            
        # 5. 再次测试标记为"无用"
        print("\n5. 再次测试标记为无用...")
        new_suggestion = "新的优化建议：重构函数逻辑"
        success, record_dict = update_review_useful(test_commit_url, 0, new_suggestion)
        if success:
            print("再次标记为无用成功")
            print(f"新建议内容: {record_dict['suggestion']}")
        else:
            print("标记失败")
            return False
            
        print("\n=== 所有测试通过 ===")
        return True
        
    except Exception as e:
        print(f"测试过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_api_compatibility():
    """测试API兼容性"""
    print("\n=== 测试API兼容性 ===")
    
    test_commit_url = "https://test.gitlab.com/project/repo/-/commit/compat_test"
    
    try:
        # 创建记录
        record_id = add_review_record(
            commit_url=test_commit_url,
            issue_count=1,
            user_name="兼容性测试",
            commit_title="兼容性测试提交"
        )
        
        # 测试不带建议的更新（应该仍然工作）
        success, record_dict = update_review_useful(test_commit_url, 1)
        if success:
            print("✓ 不带建议的API调用兼容性测试通过")
        else:
            print("✗ 不带建议的API调用失败")
            return False
            
        # 测试带建议的更新
        success, record_dict = update_review_useful(test_commit_url, 0, "兼容性测试建议")
        if success:
            print("✓ 带建议的API调用测试通过")
        else:
            print("✗ 带建议的API调用失败")
            return False
            
        print("=== API兼容性测试通过 ===")
        return True
        
    except Exception as e:
        print(f"兼容性测试失败: {str(e)}")
        return False

if __name__ == '__main__':
    print("开始测试建议功能...")
    
    # 运行测试
    test1_passed = test_suggestion_feature()
    test2_passed = test_api_compatibility()
    
    if test1_passed and test2_passed:
        print("\n🎉 所有测试都通过了！")
        sys.exit(0)
    else:
        print("\n❌ 部分测试失败")
        sys.exit(1)
